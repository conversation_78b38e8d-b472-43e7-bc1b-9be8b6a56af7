[package]
name = "kvengine"
version = "0.0.1"
authors = ["The TiKV Authors"]
license = "Apache-2.0"
edition = "2018"
publish = false

[features]
default = ["is_sync", "is_async"]
protobuf-codec = ["protobuf/bytes"]
testexport = []
is_sync = ["maybe-async/is_sync"]
is_async = ["maybe-async/is_async"]

debug-trace-ia-segments = []
debug-trace-mem-table = []

[dependencies]
aligned-vec = "0.6.1"
aliyun = { workspace = true }
anyhow = "1.0"
api_version = { path = "../api_version" }
arrow-buffer = "53.1.0"
async-once-cell = "0.5.4"
async-recursion = "1.1"
async-trait = "0.1"
aws = { path = "../cloud/aws" }
backtrace = "0.3"
base64 = "0.13"
bincode = "1.3"
bstr = "0.2"
bytemuck = "1.14.3"
byteorder = "1.2"
bytes = "1.10.1"
clara_fts = { path = "../clara_fts" }
cloud_encryption = { workspace = true }
codec = { workspace = true }
collections = { path = "../collections" }
crc32c = { workspace = true }
crc32fast = "1.4.0"
crossbeam = "0.8"
crossbeam-queue = "0.3.12"
dashmap = "4.0"
dyn-clone = "1.0"
engine_panic = { path = "../engine_panic" }
engine_rocks = { workspace = true }
engine_traits = { path = "../engine_traits", default-features = false }
fail = "0.5"
farmhash = "1.1.5"
file_system = { path = "../file_system", default-features = false }
filetime = "0.2"
fslock = "0.1.7"
futures = { version = "0.3", features = ["thread-pool"] }
hex = "0.4"
hexhex = "1.1.1"
http = "0.2"
hyper = "0.14"
hyper-rustls = "0.24.2"
hyper-tls = "0.5"
itertools = "0.10"
keys = { workspace = true }
kvenginepb = { path = "../kvenginepb", default-features = false }
kvproto = { workspace = true }
lazy_static = "1.3"
libc = "0.2"
log_wrappers = { path = "../log_wrappers" }
lz4 = "1.24"
maybe-async = { workspace = true }
memmap2 = "0.9"
nix = "0.24"
num_cpus = "1"
papaya = "0.2"
parking_lot = "0.12"
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = "2.8"
quick-xml = { version = "0.23.1", features = ["serialize"] }
quick_cache = "0.6.14"
rand = "0.8"
recovery = { workspace = true }
regex = "1"
rusoto_core = "0.46.0"
rusoto_credential = "0.46.0"
rusoto_mock = "0.46.0"
rusoto_s3 = "0.46.0"
schema = { workspace = true }
security = { workspace = true }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_json = "1.0"
slog = { version = "2.3", features = [
    "max_level_trace",
    "release_max_level_debug",
] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
stable_deref_trait = "1.2.0"
tantivy = "0.22.0"
tempfile = "3.0"
thiserror = "1.0"
tidb_query_common = { path = "../tidb_query_common", default-features = false }
tidb_query_datatype = { path = "../tidb_query_datatype", default-features = false }
tikv_alloc = { path = "../tikv_alloc" }
tikv_util = { path = "../tikv_util", default-features = false }
time = "0.1"
tipb = { workspace = true }
tokio = { version = "1.5", features = ["full"] }
tokio-util = "0.7"
tracker = { path = "../tracker" }
txn_types = { path = "../txn_types", default-features = false }
url = "2"
usearch = "2.15"
# xorf = { version = "0.8", features = ["serde"] }
xorf = { git = "https://github.com/youjiali1995/xorf", branch = "0.8.0-custom-serde", features = [
    "serde",
] }
zstd-sys = "2.0.9"

[dependencies.rocksdb]
git = "https://github.com/tikv/rust-rocksdb.git"
package = "rocksdb"
features = ["encryption"]

[dev-dependencies]
anyhow = "1.0.26"
criterion = "0.3"
proptest = "1.4"
rstest = "0.18"
tempfile = "3"
test_util = { workspace = true }
tikv_alloc = { workspace = true, features = ["jemalloc"] }
zipf = "7.0"

[[bench]]
name = "free_mem"
path = "benches/free_mem.rs"
harness = false
