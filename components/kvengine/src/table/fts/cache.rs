// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::sync::Arc;

use anyhow::Result;
use futures::Future;
use tikv_util::sys::SysQuota;

use crate::table::fts::HandleBlockAccessor;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h, PartialEq, Eq, Debug)]
pub struct HandleBlockCacheKey {
    file_id: u64,
    block_idx: usize,
}

impl HandleBlockCacheKey {
    pub fn new(file_id: u64, block_idx: usize) -> Self {
        Self { file_id, block_idx }
    }
}

#[derive(Clone)]
pub struct HandleBlockWeighter;

impl quick_cache::Weighter<HandleBlockCacheKey, Arc<HandleBlockAccessor>> for HandleBlockWeighter {
    fn weight(&self, _key: &HandleBlockCacheKey, _val: &Arc<HandleBlockAccessor>) -> u64 {
        std::mem::size_of::<HandleBlockCacheKey>() as u64
    }
}

#[repr(u8)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, De<PERSON>ult)]
pub enum CacheType {
    #[default]
    Quick = 1,
    None = 2,
}

#[derive(Clone)]
pub enum HandleBlockCache {
    Quick(
        Arc<
            quick_cache::sync::Cache<
                HandleBlockCacheKey,
                Arc<HandleBlockAccessor>,
                HandleBlockWeighter,
            >,
        >,
    ),
    None,
}

impl HandleBlockCache {
    /// Creates a new HandleBlockCache with the specified type and capacity.
    ///
    /// # Arguments
    /// * `tp` - The cache type to use
    /// * `max_capacity_bytes` - Maximum cache capacity in bytes. If 0, no cache
    ///   is used.
    ///
    /// # Returns
    /// A new HandleBlockCache instance configured with the specified
    /// parameters.
    pub fn new(tp: CacheType, max_capacity_bytes: u64) -> Self {
        if max_capacity_bytes == 0 {
            return HandleBlockCache::None;
        }
        // HANDLE_BLOCK_CACHE_SHARDS_PER_CORE determines the number of shards in the
        // cache. The total number of shards is `num_cpu_cores *
        // SHARDS_PER_CORE`. A high number of shards is used to reduce lock
        // contention in highly concurrent scenarios, prioritizing throughput
        // and scalability. 64 is a heuristic power-of-two value large enough to
        // effectively minimize contention.
        const HANDLE_BLOCK_CACHE_SHARDS_PER_CORE: usize = 64;
        let cache_shards =
            (SysQuota::cpu_cores_quota() as usize).max(1) * HANDLE_BLOCK_CACHE_SHARDS_PER_CORE;

        match tp {
            CacheType::Quick => {
                // `estimated_items_capacity` helps `quick_cache` optimize its internal
                // data structures by providing a hint about the number of items it might
                // hold.
                //
                // The actual memory usage is governed by `weight_capacity` and the
                // `HandleBlockWeighter`. The weighter only considers the size of the key,
                // because the value (`HandleBlockAccessor`) refers to mmapped data, whose
                // memory is not managed by the cache.
                let estimated_items_capacity =
                    max_capacity_bytes as usize / std::mem::size_of::<HandleBlockCacheKey>();

                let opts = quick_cache::OptionsBuilder::new()
                    .shards(cache_shards)
                    .weight_capacity(max_capacity_bytes)
                    .estimated_items_capacity(estimated_items_capacity)
                    .build()
                    .unwrap();
                let cache = quick_cache::sync::Cache::with_options(
                    opts,
                    HandleBlockWeighter,
                    quick_cache::DefaultHashBuilder::default(),
                    quick_cache::sync::DefaultLifecycle::default(),
                );
                Self::Quick(Arc::new(cache))
            }
            CacheType::None => Self::None,
        }
    }

    pub fn get(&self, key: &HandleBlockCacheKey) -> Option<Arc<HandleBlockAccessor>> {
        match self {
            Self::Quick(cache) => cache.get(key),
            Self::None => None,
        }
    }

    pub async fn try_get_with_async(
        &self,
        key: HandleBlockCacheKey,
        init: impl Future<Output = Result<Arc<HandleBlockAccessor>>>,
    ) -> Result<Arc<HandleBlockAccessor>> {
        match self {
            Self::None => init.await,
            Self::Quick(cache) => cache.get_or_insert_async(&key, init).await,
        }
    }
}
