// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{collections::BTreeMap, io::Cursor, sync::Arc};

use anyhow::Result;
use tikv_util::codec::number::NumberEncoder;

use super::*;
use crate::{
    ia::manager::tests::IaFileTester,
    table::{
        fts::{CacheType, HandleBlockCacheKey},
        ChecksumType,
    },
};

#[cfg(test)]
impl DedicatedFile<()> {
    #[cfg(test)]
    pub async fn from_buffer(buf: &[u8]) -> Result<(IaFileTester, DedicatedFile<()>)> {
        let tester = IaFileTester::default();
        let ia_file = tester
            .new_ia_file(
                crate::dfs::FileType::FtsDedicatedFile,
                buf,
                crate::table::fts::test_util::get_dedicated_file_meta_offset(buf),
            )
            .await;
        let file = Arc::new(ia_file);
        let cache = HandleBlockCache::new(CacheType::None, 0);
        Ok((tester, DedicatedFile::new(file, cache)?))
        // tester must be placed as left so that it can be dropped after
        // packed_file
    }

    #[cfg(test)]
    pub async fn from_buffer_with_cache(buf: &[u8]) -> Result<(IaFileTester, DedicatedFile<()>)> {
        let tester = IaFileTester::default();
        let ia_file = tester
            .new_ia_file(
                crate::dfs::FileType::FtsDedicatedFile,
                buf,
                crate::table::fts::test_util::get_dedicated_file_meta_offset(buf),
            )
            .await;
        let file = Arc::new(ia_file);
        let cache = HandleBlockCache::new(CacheType::Quick, 10 * 1024 * 1024 /* 10MB */);
        Ok((tester, DedicatedFile::new(file, cache)?))
        // tester must be placed as left so that it can be dropped after
        // packed_file
    }
}

#[tokio::test(flavor = "multi_thread")]
// Insert multiple handles so that DedicatedFile has multiple handle blocks,
// verify the count of handle_index_block_offset.
async fn test_finish_handle_block() -> Result<()> {
    let mut buffer = Vec::new();
    let mut options = builder::DedicatedFileBuilderOptions::default();
    options.handle_block_size = 1024; // set to 1 KB size
    let mut builder =
        builder::DedicatedFileBuilder::new(Cursor::new(&mut buffer), options, true, b"test_lp")?;

    // Add enough data to trigger handle block flushing
    // Each integer PK requires approximately 8 + 8 + 1 = 17 bytes (PK + version +
    // delete_mark) Plus other overhead, we add 100,000 PKs to exceed the
    // 1MB limit, ensuring multiple handle blocks are created
    for i in 0..1000 {
        builder.add_pk_int(i, 1000, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    let footer = dedicated_file.footer();
    assert_eq!(footer.magic, FTS_DEDICATED_FILE_MAGIC);
    assert_eq!(footer.format, FTS_DEDICATED_FILE_FORMAT_V1);

    // Verify other metadata block offsets
    assert!(footer.pk_filter_block_offset > footer.handle_index_block_offset);
    assert!(footer.prop_offset > footer.pk_filter_block_offset);

    // Verify properties can be loaded
    let props = dedicated_file.props();
    assert!(props.get_is_int_handle());
    assert_eq!(props.get_lp_key(), b"test_lp");

    // Core test: verify creation of multiple handle blocks
    // Load handle index block to check the count of handle_block_offsets
    let handle_index_block = dedicated_file.get_handle_index_block()?;

    // Verify the handle_block_offsets array
    let handle_block_offsets = &handle_index_block.handle_block_offsets;
    println!("Handle block offsets count: {}", handle_block_offsets.len());
    println!("Handle block offsets: {:?}", handle_block_offsets);

    // Since we added 100,000 PKs, each approximately 17 bytes, plus overhead,
    // the total data size is approximately 1.7MB. Therefore, multiple handle blocks
    // should be created (each block is limited to 1MB). At least 2 handle
    // blocks are expected.
    assert!(
        handle_block_offsets.len() >= 2,
        "Expected at least 2 handle block offsets (multiple blocks), got {}",
        handle_block_offsets.len()
    );

    // Verify that the offset is increasing
    for i in 1..handle_block_offsets.len() {
        assert!(
            handle_block_offsets[i] > handle_block_offsets[i - 1],
            "Handle block offsets should be increasing: offset[{}]={} should be > offset[{}]={}",
            i,
            handle_block_offsets[i],
            i - 1,
            handle_block_offsets[i - 1]
        );
    }

    // Verify that the first offset is 0 (the start of the data block)
    assert_eq!(
        handle_block_offsets[0], 0,
        "First handle block offset should be 0"
    );

    // Verify that the last offset should equal the start of the data block
    // (because the N+1 offset marks the end of the last handle block)
    let last_offset = handle_block_offsets[handle_block_offsets.len() - 1];
    assert_eq!(
        last_offset, footer.data_block_offset,
        "Last handle block offset {} should equal data_block_offset {}",
        last_offset, footer.data_block_offset
    );

    // Verify that the size between each handle block is reasonable
    for i in 1..handle_block_offsets.len() - 1 {
        let block_size = handle_block_offsets[i] - handle_block_offsets[i - 1];
        // Each handle block should be close to or exceed the 1MB limit
        assert!(
            block_size >= 1024,
            "Handle block {} size {} should be close to the 1MB limit",
            i - 1,
            block_size
        );
    }

    println!(
        "Successfully verified {} handle blocks",
        handle_block_offsets.len()
    );

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_int_handle_newer_version() -> Result<()> {
    let mut buffer = Vec::new();
    let mut options = builder::DedicatedFileBuilderOptions::default();
    options.handle_block_size = 1024; // set to 1 KB size
    let mut builder =
        builder::DedicatedFileBuilder::new(Cursor::new(&mut buffer), options, true, b"test_lp")?;

    for i in 0..20 {
        builder.add_pk_int(100, 50 - i as u64, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    let result = dedicated_file.has_newer_version_int(100, 40, 50).await?;
    assert!(result);

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_common_handle_newer_version() -> Result<()> {
    let mut buffer = Vec::new();
    let mut options = builder::DedicatedFileBuilderOptions::default();
    options.handle_block_size = 1024; // set to 1 KB size
    let mut builder =
        builder::DedicatedFileBuilder::new(Cursor::new(&mut buffer), options, false, b"test_lp")?;

    let handle = "handle_111";
    for i in 0..20 {
        builder.add_pk_common(handle.as_bytes(), 50 - i as u64, false)?; // version=100, delete_mark=false
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    let result = dedicated_file
        .has_newer_version_common(handle.as_bytes(), 40, 50)
        .await?;
    assert!(result);

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_multi_block_int_handle_search_with_negative_positive() -> Result<()> {
    let mut buffer = Vec::new();
    let options = builder::DedicatedFileBuilderOptions::default();
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        options,
        true,
        b"test_lp_mixed",
    )?;

    // Add mixed negative and positive integer PKs in ascending order
    // Range: -75000 to +74999 (total 150,000 PKs)
    // This ensures we have both negative and positive values
    for i in -75i64..75i64 {
        builder.add_pk_int(i, 100, false)?; // Mark every 1000th PK as deleted
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    // Test cases with both negative and positive handles
    let test_cases = vec![
        (-50i64, "large negative"),
        (-1i64, "small negative"),
        (0i64, "zero"),
        (1i64, "small positive"),
        (50i64, "large positive"),
    ];

    for (handle_value, description) in test_cases {
        println!("Testing {} handle: {}", description, handle_value);

        let result = dedicated_file
            .has_newer_version_int(handle_value, 40, 100)
            .await?;
        assert!(result);
    }

    // Test boundary cases
    let boundary_cases = vec![(-75i64, "minimum value"), (74i64, "maximum value")];

    for (handle_value, description) in boundary_cases {
        println!("Testing boundary case {}: {}", description, handle_value);

        let result = dedicated_file
            .has_newer_version_int(handle_value, 40, 100)
            .await?;
        assert!(result);
    }

    // Test out-of-range cases
    let out_of_range_cases = vec![(-1000i64, "below minimum"), (1000i64, "above maximum")];

    for (handle_value, description) in out_of_range_cases {
        println!(
            "Testing out-of-range case {}: {}",
            description, handle_value
        );

        let result = dedicated_file
            .has_newer_version_int(handle_value, 40, 100)
            .await?;
        assert!(!result);
    }

    println!("🎉 All mixed negative/positive integer handle tests passed!");
    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_handle_block_checksum_corruption() -> Result<()> {
    // This test verifies checksum mechanism and data integrity checks
    // Build a valid DedicatedFile with checksum enabled
    let mut buffer = Vec::new();
    let options = builder::DedicatedFileBuilderOptions::default();
    let mut builder = builder::DedicatedFileBuilder::new(
        &mut buffer,
        options,
        true, // Integer PK
        b"test_lp_checksum",
    )?;

    // Add some test data
    for i in 0..1000 {
        builder.add_pk_int(i, 10000 - i as u64, i % 50 == 0)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Test Case 1: Handle Block Corruption
    {
        let mut corrupted_buffer = buffer.clone();

        // Corrupt a byte in the handle block area (near the beginning)
        corrupted_buffer[20] ^= 0xFF;

        // DedicatedFile::new() should succeed because metadata and footer are intact
        let (_g, dedicated_file) = DedicatedFile::from_buffer(&corrupted_buffer).await?;

        // Accessing the corrupted handle block should fail
        let err = dedicated_file
            .has_newer_version_int(0, 50, 200)
            .await
            .unwrap_err();

        assert!(err.to_string().contains("Handle block checksum mismatch"));
    }

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_footer_marshal_unmarshal_with_checksum() {
    let mut f = DedicatedFileFooter::new();
    f.checksum_type = ChecksumType::Crc32c;
    f.checksum_other_meta = 0xA1B2_C3D4;
    f.data_block_offset = 0x10_20_30_40;
    f.pk_filter_block_offset = 0x20_30_40_50;
    f.handle_index_block_offset = 0x30_40_50_60;
    f.prop_offset = 0x40_50_60_70;

    let mut buf = Vec::new();
    f.marshal(&mut buf).unwrap();

    // Roundtrip test - unmarshal and verify all fields match
    let f2 = DedicatedFileFooter::unmarshal(&buf).unwrap();
    assert_eq!(f2.format, f.format);
    assert_eq!(f2.checksum_type.value(), f.checksum_type.value());
    assert_eq!(f2.checksum_other_meta, f.checksum_other_meta);
    assert_eq!(f2.data_block_offset, f.data_block_offset);
    assert_eq!(f2.pk_filter_block_offset, f.pk_filter_block_offset);
    assert_eq!(f2.handle_index_block_offset, f.handle_index_block_offset);
    assert_eq!(f2.prop_offset, f.prop_offset);
    assert_eq!(f2.magic, f.magic);

    // Modify checksum field should cause checksum mismatch
    let mut buf2 = buf.clone();
    buf2[8] ^= 0xFF; // Flip a bit in checksum field
    let result = DedicatedFileFooter::unmarshal(&buf2);
    assert!(
        result.is_err(),
        "Expected checksum mismatch when checksum field is corrupted"
    );
    let error_msg = result.unwrap_err().to_string();
    assert!(
        error_msg.contains("FtsDedicatedFile footer checksum mismatch"),
        "Error should mention checksum mismatch"
    );
}

#[tokio::test(flavor = "multi_thread")]
async fn test_last_int_handle_block_access() -> Result<()> {
    // This test specifically targets the bug where accessing the last handle block
    // would cause an out-of-bounds error due to incorrect N vs N+1 offset array
    // handling

    let mut buffer = Vec::new();
    let mut options = builder::DedicatedFileBuilderOptions::default();
    options.handle_block_size = 1024; // set to 1 KB size
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        options,
        true, // Integer PK
        b"test_lp",
    )?;

    // Add enough data to create multiple handle blocks
    // With small block size, this should create several blocks
    for i in 0..1000 {
        builder.add_pk_int(i, 200, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Create IaFile from buffer using the same pattern as other tests
    let (_g, dedicated_file) = DedicatedFile::from_buffer_with_cache(&buffer).await?;

    // Test PKs that should definitely be in the last block
    // We need to test PKs that are >= last_block_start_pk
    let test_cases = vec![
        990i64, // First PK in last block
        995i64, // Second PK in last block (if exists)
        999i64, // Highest PK we added (should be in last block)
    ];

    let handle_index = dedicated_file.get_handle_index_block()?;

    for pk in test_cases {
        let mut key = Vec::with_capacity(64);
        key.encode_i64(pk).unwrap();
        let Some(idx) = dedicated_file.find_handle_block_index(key.as_slice())? else {
            bail!("Should find handle block containing PK {:?}", pk);
        };
        assert_eq!(idx, handle_index.handle_block_start_key.len() - 1);

        // This call should not panic or cause out-of-bounds errors
        let result = dedicated_file.has_newer_version_int(pk, 50, 200).await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // test cache init
        let cache_key = HandleBlockCacheKey::new(dedicated_file.0.file.id(), idx);
        if let HandleBlockCache::Quick(cache) = &dedicated_file.0.handle_block_accessor_cache {
            assert!(cache.get(&cache_key).is_some());
        }

        let result = dedicated_file.has_newer_version_int(pk, 300, 200).await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }
    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_last_common_handle_block_access() -> Result<()> {
    // This test specifically targets the bug where accessing the last handle block
    // would cause an out-of-bounds error due to incorrect N vs N+1 offset array
    // handling This version tests common handles (string PKs) instead of
    // integer PKs

    let mut buffer = Vec::new();
    let options = builder::DedicatedFileBuilderOptions::default();
    let mut builder = builder::DedicatedFileBuilder::new(
        &mut buffer, // Use &mut buffer instead of Cursor
        options,
        false, // Common Handle (string PK)
        b"test_lp",
    )?;

    // Add enough data to create multiple handle blocks
    // Use string PKs with consistent format for proper lexicographic ordering
    // Use fewer PKs but still enough to create multiple blocks
    for i in 0..10000 {
        let pk_str = format!("handle_{:08}", i); // e.g., "handle_00000000", "handle_00000001"
        builder.add_pk_common(pk_str.as_bytes(), 200, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Create IaFile from buffer using the same pattern as other tests
    let (_g, dedicated_file) = DedicatedFile::from_buffer_with_cache(&buffer).await?;

    // Test PKs that should definitely be in the last block
    // For common handles, we need to be more careful about which PKs are in the
    // last block Let's test PKs that are very likely to be in the last few
    // blocks
    let test_cases = vec![
        b"handle_00009997".to_vec(), // Near the end
        b"handle_00009998".to_vec(), // Near the end
        b"handle_00009999".to_vec(), // Highest PK we added (should be in last block)
    ];

    let handle_index = dedicated_file.get_handle_index_block()?;

    for pk_bytes in test_cases {
        let Some(idx) = dedicated_file.find_handle_block_index(pk_bytes.as_slice())? else {
            bail!("Should find handle block containing PK {:?}", pk_bytes);
        };
        assert_eq!(idx, handle_index.handle_block_start_key.len() - 1);

        // This call should not panic or cause out-of-bounds errors
        let result = dedicated_file
            .has_newer_version_common(&pk_bytes, 50, 200)
            .await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // test cache init
        let cache_key = HandleBlockCacheKey::new(dedicated_file.0.file.id(), idx);
        if let HandleBlockCache::Quick(cache) = &dedicated_file.0.handle_block_accessor_cache {
            assert!(cache.get(&cache_key).is_some());
        }

        let result = dedicated_file
            .has_newer_version_common(&pk_bytes, 300, 200)
            .await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }
    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_handle_block_cache_hit_and_sharing() -> Result<()> {
    // dedicated_file_1
    let mut buffer = Vec::new();
    let mut options = builder::DedicatedFileBuilderOptions::default();
    options.handle_block_size = 1024; // set to 1 KB size
    let mut builder = builder::DedicatedFileBuilder::new(&mut buffer, options, true, b"test_lp")?;

    for i in 0..1000 {
        builder.add_pk_int(i, 200, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let tester = IaFileTester::default();
    let ia_file = tester
        .new_ia_file(
            crate::dfs::FileType::FtsDedicatedFile,
            buffer.as_slice(),
            crate::table::fts::test_util::get_dedicated_file_meta_offset(buffer.as_slice()),
        )
        .await;
    let file = Arc::new(ia_file);
    let cache = HandleBlockCache::new(CacheType::Quick, 10 * 1024 * 1024 /* 10MB */);
    let dedicated_file_1 = DedicatedFile::new(file, cache.clone())?;

    let handle_index_1 = dedicated_file_1.get_handle_index_block()?;

    let mut key = Vec::with_capacity(64);
    key.encode_i64(990i64).unwrap();
    let Some(idx1) = dedicated_file_1.find_handle_block_index(key.as_slice())? else {
        bail!("Should find handle block containing PK {:?}", 990);
    };
    assert_eq!(idx1, handle_index_1.handle_block_start_key.len() - 1);

    let result = dedicated_file_1.has_newer_version_int(990, 50, 200).await;
    assert!(result.is_ok());
    assert!(result.unwrap());

    // dedicated_file_2
    let mut buffer2 = Vec::new();
    let mut options = builder::DedicatedFileBuilderOptions::default();
    options.handle_block_size = 1024; // set to 1 KB size
    let mut builder2 =
        builder::DedicatedFileBuilder::new(&mut buffer2, options, true, b"test_lp_2")?;

    for i in 2000..3000 {
        builder2.add_pk_int(i, 200, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder2.finish(tantivy_files)?;

    let ia_file = tester
        .new_ia_file(
            crate::dfs::FileType::FtsDedicatedFile,
            buffer2.as_slice(),
            crate::table::fts::test_util::get_dedicated_file_meta_offset(buffer2.as_slice()),
        )
        .await;
    let file = Arc::new(ia_file);
    let dedicated_file_2 = DedicatedFile::new(file, cache.clone())?;

    let handle_index_2 = dedicated_file_2.get_handle_index_block()?;

    let mut key = Vec::with_capacity(64);
    key.encode_i64(2990).unwrap();
    let Some(idx2) = dedicated_file_2.find_handle_block_index(key.as_slice())? else {
        bail!("Should find handle block containing PK {:?}", 2990);
    };
    assert_eq!(idx2, handle_index_2.handle_block_start_key.len() - 1);

    let result = dedicated_file_2.has_newer_version_int(2990, 50, 200).await;
    assert!(result.is_ok());
    assert!(result.unwrap());

    // test cache hit
    let cache_key1 = HandleBlockCacheKey::new(dedicated_file_1.0.file.id(), idx1);
    let cache_key2 = HandleBlockCacheKey::new(dedicated_file_2.0.file.id(), idx2);
    if let HandleBlockCache::Quick(cache) = &dedicated_file_1.0.handle_block_accessor_cache {
        assert!(cache.get(&cache_key1).is_some());
        assert!(cache.get(&cache_key2).is_some());
    }

    Ok(())
}
