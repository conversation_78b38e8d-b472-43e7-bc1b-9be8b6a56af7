// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use tantivy::directory::MmapDirectory;
use tantivy::indexer::merge_indices;
use tantivy::Index;
use tantivy::TantivyError;

pub struct FtsIndexMerger {
    indexes: Vec<Index>,
    directory: MmapDirectory,
}

impl FtsIndexMerger {
    pub fn new(indexes : Vec<Index>, directory: MmapDirectory) -> Self {
        Self {
            indexes,
            directory,
        }
    }

    pub fn merge(self) -> tantivy::Result<Index> {
        let merged_index = merge_indices(&self.indexes, self.directory)?;
        let segment_count = merged_index.searchable_segment_ids()?.len();
        if segment_count != 1 {
            return tantivy::Result::Err(TantivyError::InternalError(format!(
                "Expected 1 segment, got {}",
                segment_count
            )));
        }

        Ok(merged_index)
    }
}

#[cfg(test)]
mod tests {
    use std::path::Path;

    use super::*;
    use tempfile::TempDir;
    use tantivy::directory::MmapDirectory;
    use tantivy::schema::{FAST, INDEXED, Field};
    use tantivy::{IndexSettings, IndexSortByField, Order, IndexWriter};
    use tantivy::doc;
    use tantivy::collector::TopDocs;
    use std::collections::BTreeMap;
    use crate::table::fts::BytesDirectory;
    use anyhow::{bail, Result};
    use crate::table::fts::test_util::{IndexWriterOnDisk, IndexWriterInMemory};

    struct TempFile {
        pub temp_file_1: TempDir,
        pub temp_file_2: TempDir,
    }

    impl TempFile {
        fn new() -> Self {
            Self {
                temp_file_1: TempDir::new().unwrap(),
                temp_file_2: TempDir::new().unwrap(),
            }
        }
    }

    fn create_fts_index(i: usize, temp_file: &TempFile, tokenizer: &str) -> Result<Index> {
        let temp_dir = if i == 1 {
            temp_file.temp_file_1.path()
        } else {
            temp_file.temp_file_2.path()
        };
        let directory = MmapDirectory::open(temp_dir)?;

        let mut schema_builder = tantivy::schema::Schema::builder();

        schema_builder.add_i64_field("pk_dummy", FAST | INDEXED);

        schema_builder.add_text_field(
            "body",
            tantivy::schema::TextOptions::default()
                .set_indexing_options(
                    tantivy::schema::TextFieldIndexing::default()
                        .set_tokenizer(tokenizer)
                        .set_fieldnorms(true)
                        .set_index_option(tantivy::schema::IndexRecordOption::WithFreqsAndPositions),
                ),
        );

        let schema = schema_builder.build();

        // 关键：设置按PK字段排序
        let settings = IndexSettings {
            sort_by_field: Some(IndexSortByField {
                field: "pk_dummy".to_string(),
                order: Order::Asc,
            }),
            ..Default::default()
        };

        let mut index = Index::create(directory, schema, settings)?;
        index.set_tokenizers(clara_fts::TOKENIZERS.clone());

        Ok(index)
    }

    pub fn create_bytes_directory_from_files(tantivy_files: &BTreeMap<String, Vec<u8>>) -> Result<BytesDirectory> {
        use bytes::Bytes;

        // Convert Vec<u8> to Bytes for efficient access
        let get_bytes = |key: &str| -> Result<Bytes> {
            if let Some(data) = tantivy_files.get(key) {
                Ok(Bytes::from(data.clone()))
            } else {
                if key == "json1" {
                    return Ok(Bytes::new());
                }
                bail!("Missing file: {}", key);
            }
        };

        Ok(BytesDirectory {
            meta: get_bytes("json0")?,        // meta.json
            managed: get_bytes("json1")?,     // .managed.json
            term: get_bytes("term")?,         // *.term
            idx: get_bytes("idx")?,           // *.idx
            pos: get_bytes("pos")?,           // *.pos
            store: get_bytes("store")?,       // *.store
            fast: get_bytes("fast")?,         // *.fast
            fieldnorm: get_bytes("fieldnorm")?, // *.fieldnorm
        })
    }

    fn create_bytes_directory_from_buffer(buffer: Vec<u8>) -> Result<BytesDirectory> {
        let decoder = clara_fts::MergedFileDecoder::from_buffer(buffer)?;
        for file_path in decoder.list_files() {
            let key = file_path
                .file_name()
                .and_then(|n| n.to_str())
                .and_then(|name| match name {
                    "meta.json" => Some("json0"),
                    ".managed.json" => Some("json1"),
                    s if s.ends_with(".term") => Some("term"),
                    s if s.ends_with(".idx") => Some("idx"),
                    s if s.ends_with(".pos") => Some("pos"),
                    s if s.ends_with(".store") => Some("store"),
                    s if s.ends_with(".fast") => Some("fast"),
                    s if s.ends_with(".fieldnorm") => Some("fieldnorm"),
                    _ => None,
                });

            if let Some(key) = key {
                // Read file content immediately while temp_dir is still alive
                let full_path = temp_dir.join(&file_path);
                let data = std::fs::read(&full_path)?;
                tantivy_files.insert(key.to_string(), data);
            }
        }
        Ok(())
    }

    fn add_document(index_writer: &IndexWriter, pk_field: Field, body_field: Field, pk: i64, body: &str) -> Result<()> {
        index_writer.add_document(doc!(pk_field => pk, body_field => body))?;
        Ok(())
    }

    fn get_body(index: &Index) -> Result<Field>{
        let body_field = index.schema().get_field("body").unwrap();
        Ok(body_field)
    }

    fn get_pk(index: &Index) -> Result<Field>{
        let pk_field = index.schema().get_field("pk_dummy").unwrap();
        Ok(pk_field)
    }

    async fn finish(writer: &mut IndexWriter, temp_dir: &Path) -> Result<BTreeMap<String, Vec<u8>>> {
        writer.commit()?;

        let searchable_segment_ids = {
            let index = writer.index();
            index.searchable_segment_ids()?
        };
        if !searchable_segment_ids.is_empty() {
            writer.merge(&searchable_segment_ids).await?;
            // A commit is necessary to publish the merge and delete old segments.
            writer.commit()?;
        }

        {
            let index = writer.index();
            let len = index.searchable_segment_ids()?.len();
            assert_eq!(len, 1, "Expected 1 segment, got {}", len);
        }

        let index = writer.index();
        let directory = index.directory();
        let file_lists = directory.list_managed_files();
        let mut tantivy_files = BTreeMap::new();
        for file_path in file_lists {
            let key = file_path
                .file_name()
                .and_then(|n| n.to_str())
                .and_then(|name| match name {
                    "meta.json" => Some("json0"),
                    ".managed.json" => Some("json1"),
                    s if s.ends_with(".term") => Some("term"),
                    s if s.ends_with(".idx") => Some("idx"),
                    s if s.ends_with(".pos") => Some("pos"),
                    s if s.ends_with(".store") => Some("store"),
                    s if s.ends_with(".fast") => Some("fast"),
                    s if s.ends_with(".fieldnorm") => Some("fieldnorm"),
                    _ => None,
                });

            if let Some(key) = key {
                // Read file content immediately while temp_dir is still alive
                let full_path = temp_dir.join(&file_path);
                let data = std::fs::read(&full_path)?;
                tantivy_files.insert(key.to_string(), data);
            }
        }

        Ok(tantivy_files)
    }

    #[tokio::test(flavor = "multi_thread")]
    async fn test_fts_index_builder_basic() -> Result<()> {
        println!("=== 开始测试索引构建和合并 ===");

        // === 构建第一个索引 ===
        println!("构建索引1...");
        let temp_dir = TempFile::new();
        let index = create_fts_index(1, &temp_dir, "STANDARD_V1")?;
        let body_field = get_body(&index)?;
        let pk_field = get_pk(&index)?;
        let mut index_writer = index.writer(15 * 1024 * 1024)?;
        add_document(&index_writer, pk_field, body_field, 1, "hello world")?;
        add_document(&index_writer, pk_field, body_field, 3, "tantivy search engine")?;
        add_document(&index_writer, pk_field, body_field, 5, "full text search")?;
        let tantivy_files = finish(&mut index_writer, temp_dir.temp_file_1.path()).await?;

        let bytes_dir = create_bytes_directory_from_files(&tantivy_files)?;
        let mut index = tantivy::Index::open(bytes_dir)?;
        index.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // 验证第一个索引的 PK 排序
        println!("验证索引1的 PK-doc_id 映射:");
        verify_pk_doc_id_mapping(&index, &[(1, "hello world"), (3, "tantivy search engine"), (5, "full text search")])?;

        // === 构建第二个索引 ===
        println!("构建索引2...");
        let index2 = create_fts_index(2, &temp_dir, "STANDARD_V1")?;
        let mut index_writer2 = index2.writer(15 * 1024 * 1024)?;
        add_document(&index_writer2, pk_field, body_field, 2, "machine learning")?;
        add_document(&index_writer2, pk_field, body_field, 4, "search test in index_2")?;
        add_document(&index_writer2, pk_field, body_field, 6, "no document search in index_2")?;

        let tantivy_files2 = finish(&mut index_writer2, temp_dir.temp_file_2.path()).await?;

        let bytes_dir2 = create_bytes_directory_from_files(&tantivy_files2)?;
        let mut index2 = tantivy::Index::open(bytes_dir2)?;
        index2.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // 验证第二个索引的 PK 排序
        println!("验证索引2的 PK-doc_id 映射:");
        verify_pk_doc_id_mapping(&index2, &[(2, "machine learning"), (4, "search test in index_2"), (6, "no document search in index_2")])?;

        // === 合并索引 ===
        println!("=== 合并索引 ===");
        let merger = FtsIndexMerger::new(vec![index, index2], MmapDirectory::create_from_tempdir()?);
        let mut index_merge = merger.merge()?;
        index_merge.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // === 验证合并后的 PK-doc_id 约束关系 ===
        println!("=== 验证合并后的约束关系 ===");

        // 期望的合并后顺序：按 PK 升序排列
        let expected_merged_data = vec![
            (1, "hello world"),
            (2, "machine learning"),
            (3, "tantivy search engine"),
            (4, "search test in index_2"),
            (5, "full text search"),
            (6, "no document search in index_2"),
        ];

        verify_pk_doc_id_mapping(&index_merge, &expected_merged_data)?;

        // === 验证搜索功能在合并后仍然正常 ===
        println!("=== 验证搜索功能 ===");
        let reader_merge = index_merge.reader()?;
        let searcher_merge = reader_merge.searcher();
        let query_parser_merge = tantivy::query::QueryParser::for_index(&index_merge, vec![body_field]);

        // 测试搜索 "search" - 应该找到 4 个文档
        {
            let query = query_parser_merge.parse_query("search")?;
            let top_docs = searcher_merge.search(&query, &TopDocs::with_limit(10))?;
            println!("搜索 'search' 找到 {} 个结果", top_docs.len());

            let mut found_pks = Vec::new();
            for (score, doc_address) in top_docs {
                let segment_reader = searcher_merge.segment_reader(doc_address.segment_ord);
                let pk_accessor = segment_reader.fast_fields().i64("pk_dummy").unwrap();
                let pk_value = pk_accessor.values.get_val(doc_address.doc_id);
                found_pks.push(pk_value);
                println!("  找到: PK={}, doc_id={}, score={:.4}", pk_value, doc_address.doc_id, score);
            }

            // 验证找到的 PK 是正确的
            assert!(found_pks.contains(&3)); // "tantivy search engine"
            assert!(found_pks.contains(&4)); // "search test in index_2" 
            assert!(found_pks.contains(&5)); // "full text search"
            assert!(found_pks.contains(&6)); // "no document search in index_2"
            assert_eq!(found_pks.len(), 4);
        }

        // 测试搜索 "machine" - 应该只找到 1 个文档
        {
            let query = query_parser_merge.parse_query("machine")?;
            let top_docs = searcher_merge.search(&query, &TopDocs::with_limit(10))?;
            assert_eq!(top_docs.len(), 1);

            let (_, doc_address) = &top_docs[0];
            let segment_reader = searcher_merge.segment_reader(doc_address.segment_ord);
            let pk_accessor = segment_reader.fast_fields().i64("pk_dummy").unwrap();
            let pk_value = pk_accessor.values.get_val(doc_address.doc_id);

            assert_eq!(pk_value, 2); // "machine learning"
            println!("搜索 'machine' 找到: PK={}, doc_id={}", pk_value, doc_address.doc_id);
        }

        // === 最终验证：通过 doc_id 获取 PK 的完整性测试 ===
        println!("=== 完整性验证：doc_id -> PK 映射 ===");
        let segment_reader = searcher_merge.segment_reader(0);
        let pk_accessor = segment_reader.fast_fields().i64("pk_dummy").unwrap();

        for doc_id in 0..segment_reader.num_docs() {
            let pk_value = pk_accessor.values.get_val(doc_id);
            let expected_pk = expected_merged_data[doc_id as usize].0;

            assert_eq!(pk_value, expected_pk,
                "doc_id {} 的 PK 应该是 {}, 但实际是 {}",
                doc_id, expected_pk, pk_value);

            println!("✅ doc_id {} -> PK {} (预期: {})", doc_id, pk_value, expected_pk);
        }

        println!("=== 测试完成！所有约束关系验证通过 ===");
        Ok(())
    }

    // 辅助函数：验证索引的 PK-doc_id 映射关系
    fn verify_pk_doc_id_mapping(index: &Index, expected_data: &[(i64, &str)]) -> Result<()> {
        let reader = index.reader()?;
        let searcher = reader.searcher();

        // 确保只有一个 segment
        assert_eq!(searcher.segment_readers().len(), 1, "索引应该只有一个 segment");

        let segment_reader = searcher.segment_reader(0);
        let pk_accessor = segment_reader.fast_fields().i64("pk_dummy").unwrap();

        // 验证文档数量
        assert_eq!(segment_reader.num_docs(), expected_data.len() as u32,
            "文档数量不匹配");

        // 验证每个 doc_id 对应的 PK 是否按升序排列
        for doc_id in 0..segment_reader.num_docs() {
            let pk_value = pk_accessor.values.get_val(doc_id);
            let expected_pk = expected_data[doc_id as usize].0;

            assert_eq!(pk_value, expected_pk,
                "doc_id {} 的 PK 应该是 {}, 但实际是 {}",
                doc_id, expected_pk, pk_value);

            println!("  doc_id {} -> PK {} ✅", doc_id, pk_value);
        }

        // 验证 PK 确实是升序排列的
        for i in 1..segment_reader.num_docs() {
            let prev_pk = pk_accessor.values.get_val(i - 1);
            let curr_pk = pk_accessor.values.get_val(i);
            assert!(prev_pk < curr_pk,
                "PK 没有正确排序: doc_id {} PK {} 应该小于 doc_id {} PK {}",
                i-1, prev_pk, i, curr_pk);
        }

        println!("  ✅ PK 按升序正确排列");
        Ok(())
    }

    #[tokio::test(flavor = "multi_thread")]
    async fn test_fts_index() -> Result<()> {
        println!("=== 开始测试索引构建和合并 ===");

        // === 构建第一个索引 ===
        println!("构建索引1...");
        let temp_dir = TempFile::new();
        let index_writer = IndexWriterInMemory::new("STANDARD_V1")?;
        index_writer.add_document(1, "hello world")?;
        index_writer.add_document(3, "tantivy search engine")?;
        index_writer.add_document(5, "full text search")?;
        let index_data = index_writer.finalize()?;
        let index_decoder = clara_fts::MergedFileDecoder::from_buffer(index_data)?;

        let bytes_dir = create_bytes_directory_from_files(&tantivy_files)?;
        let mut index = tantivy::Index::open(bytes_dir)?;
        index.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // 验证第一个索引的 PK 排序
        println!("验证索引1的 PK-doc_id 映射:");
        verify_pk_doc_id_mapping(&index, &[(1, "hello world"), (3, "tantivy search engine"), (5, "full text search")])?;

        // === 构建第二个索引 ===
        println!("构建索引2...");
        let index2 = create_fts_index(2, &temp_dir, "STANDARD_V1")?;
        let mut index_writer2 = index2.writer(15 * 1024 * 1024)?;
        add_document(&index_writer2, pk_field, body_field, 2, "machine learning")?;
        add_document(&index_writer2, pk_field, body_field, 4, "search test in index_2")?;
        add_document(&index_writer2, pk_field, body_field, 6, "no document search in index_2")?;

        let tantivy_files2 = finish(&mut index_writer2, temp_dir.temp_file_2.path()).await?;

        let bytes_dir2 = create_bytes_directory_from_files(&tantivy_files2)?;
        let mut index2 = tantivy::Index::open(bytes_dir2)?;
        index2.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // 验证第二个索引的 PK 排序
        println!("验证索引2的 PK-doc_id 映射:");
        verify_pk_doc_id_mapping(&index2, &[(2, "machine learning"), (4, "search test in index_2"), (6, "no document search in index_2")])?;

        // === 合并索引 ===
        println!("=== 合并索引 ===");
        let merger = FtsIndexMerger::new(vec![index, index2], MmapDirectory::create_from_tempdir()?);
        let mut index_merge = merger.merge()?;
        index_merge.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // === 验证合并后的 PK-doc_id 约束关系 ===
        println!("=== 验证合并后的约束关系 ===");

        // 期望的合并后顺序：按 PK 升序排列
        let expected_merged_data = vec![
            (1, "hello world"),
            (2, "machine learning"),
            (3, "tantivy search engine"),
            (4, "search test in index_2"),
            (5, "full text search"),
            (6, "no document search in index_2"),
        ];

        verify_pk_doc_id_mapping(&index_merge, &expected_merged_data)?;

        // === 验证搜索功能在合并后仍然正常 ===
        println!("=== 验证搜索功能 ===");
        let reader_merge = index_merge.reader()?;
        let searcher_merge = reader_merge.searcher();
        let query_parser_merge = tantivy::query::QueryParser::for_index(&index_merge, vec![body_field]);

        // 测试搜索 "search" - 应该找到 4 个文档
        {
            let query = query_parser_merge.parse_query("search")?;
            let top_docs = searcher_merge.search(&query, &TopDocs::with_limit(10))?;
            println!("搜索 'search' 找到 {} 个结果", top_docs.len());

            let mut found_pks = Vec::new();
            for (score, doc_address) in top_docs {
                let segment_reader = searcher_merge.segment_reader(doc_address.segment_ord);
                let pk_accessor = segment_reader.fast_fields().i64("pk_dummy").unwrap();
                let pk_value = pk_accessor.values.get_val(doc_address.doc_id);
                found_pks.push(pk_value);
                println!("  找到: PK={}, doc_id={}, score={:.4}", pk_value, doc_address.doc_id, score);
            }

            // 验证找到的 PK 是正确的
            assert!(found_pks.contains(&3)); // "tantivy search engine"
            assert!(found_pks.contains(&4)); // "search test in index_2" 
            assert!(found_pks.contains(&5)); // "full text search"
            assert!(found_pks.contains(&6)); // "no document search in index_2"
            assert_eq!(found_pks.len(), 4);
        }

        // 测试搜索 "machine" - 应该只找到 1 个文档
        {
            let query = query_parser_merge.parse_query("machine")?;
            let top_docs = searcher_merge.search(&query, &TopDocs::with_limit(10))?;
            assert_eq!(top_docs.len(), 1);

            let (_, doc_address) = &top_docs[0];
            let segment_reader = searcher_merge.segment_reader(doc_address.segment_ord);
            let pk_accessor = segment_reader.fast_fields().i64("pk_dummy").unwrap();
            let pk_value = pk_accessor.values.get_val(doc_address.doc_id);

            assert_eq!(pk_value, 2); // "machine learning"
            println!("搜索 'machine' 找到: PK={}, doc_id={}", pk_value, doc_address.doc_id);
        }

        // === 最终验证：通过 doc_id 获取 PK 的完整性测试 ===
        println!("=== 完整性验证：doc_id -> PK 映射 ===");
        let segment_reader = searcher_merge.segment_reader(0);
        let pk_accessor = segment_reader.fast_fields().i64("pk_dummy").unwrap();

        for doc_id in 0..segment_reader.num_docs() {
            let pk_value = pk_accessor.values.get_val(doc_id);
            let expected_pk = expected_merged_data[doc_id as usize].0;

            assert_eq!(pk_value, expected_pk,
                "doc_id {} 的 PK 应该是 {}, 但实际是 {}",
                doc_id, expected_pk, pk_value);

            println!("✅ doc_id {} -> PK {} (预期: {})", doc_id, pk_value, expected_pk);
        }

        println!("=== 测试完成！所有约束关系验证通过 ===");
        Ok(())
    }
}