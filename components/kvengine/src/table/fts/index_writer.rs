// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::collections::BTreeMap;

use anyhow::{bail, Result};
use tantivy::schema::NumericOptions;
use tempfile::TempDir;
use tantivy::doc;

pub const FTS_IDNEX_WRITER_MAX_MEMORY_USAGE: usize = 200 * 1024 * 1024; // 200MB

pub struct FtsIndexBuilder {
    pub index_writer: tantivy::IndexWriter,
    field_body: tantivy::schema::Field,
    pk_field: tantivy::schema::Field,
    temp_dir: TempDir,
}

impl FtsIndexBuilder {
    pub fn new(tokenizer: &str) -> Result<Self> {
        let temp_dir = TempDir::new()?;
        let mut schema_builder = tantivy::schema::Schema::builder();

        // Add primary key field - numeric fields don't need tokenizers
        let pk_options = NumericOptions::default()
            .set_indexed()
            .set_fast()
            .set_stored();
        let pk_field = schema_builder.add_u64_field("pk", pk_options);

        // Add body field for full-text search
        let field_body = schema_builder.add_text_field(
            "body",
            tantivy::schema::TextOptions::default().set_indexing_options(
                tantivy::schema::TextFieldIndexing::default()
                    .set_tokenizer(tokenizer)
                    .set_fieldnorms(true)
                    .set_index_option(tantivy::schema::IndexRecordOption::WithFreqsAndPositions),
            ),
        );

        let schema = schema_builder.build();

        // Create index using IndexBuilder with tokenizers
        let index = tantivy::IndexBuilder::new()
            .tokenizers(clara_fts::TOKENIZERS.clone())
            .schema(schema)
            .create_in_dir(temp_dir.path())?;

        let index_writer: tantivy::IndexWriter = index.writer(FTS_IDNEX_WRITER_MAX_MEMORY_USAGE)?;

        Ok(Self {
            index_writer,
            field_body,
            pk_field,
            temp_dir,
        })
    }

    pub fn add_intpk_document(&mut self, pk: u64, body: &str) -> Result<()> {
        self.index_writer.add_document(doc!(self.pk_field => pk, self.field_body => body))?;
        Ok(())
    }

    pub async fn finish(mut self) -> Result<BTreeMap<String, std::path::PathBuf>> {
        // Step 1: Commit and finalize the index
        self.index_writer.commit()?;

        let searchable_segment_ids = {
            let index = self.index_writer.index();
            index.searchable_segment_ids()?
        };
        if !searchable_segment_ids.is_empty() {
            self.index_writer.merge(&searchable_segment_ids).await?;
            // A commit is necessary to publish the merge and delete old segments.
            self.index_writer.commit()?;
        }

        {
            let index = self.index_writer.index();
            let len = index.searchable_segment_ids()?.len();
            assert_eq!(len, 1, "Expected 1 segment, got {}", len);
        }

        let index = self.index_writer.index();
        let directory = index.directory();
        let file_lists = directory.list_managed_files();
        let mut tantivy_files = BTreeMap::new();
        for file_path in file_lists {
            let key = file_path
                .file_name()
                .and_then(|n| n.to_str())
                .and_then(|name| match name {
                    "meta.json" => Some("json0"),
                    ".managed.json" => Some("json1"),
                    s if s.ends_with(".term") => Some("term"),
                    s if s.ends_with(".idx") => Some("idx"),
                    s if s.ends_with(".pos") => Some("pos"),
                    s if s.ends_with(".store") => Some("store"),
                    s if s.ends_with(".fast") => Some("fast"),
                    s if s.ends_with(".fieldnorm") => Some("fieldnorm"),
                    _ => None,
                });

            if let Some(key) = key {
                // Store only the file path, not the file content to maintain streaming design
                let full_path = self.temp_dir.path().join(&file_path);
                tantivy_files.insert(key.to_string(), full_path);
            }
        }

        Ok(tantivy_files)
    }

    pub fn get_dir(self) -> TempDir {
        self.temp_dir
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test(flavor = "multi_thread")]
    async fn test_fts_index_builder_basic() -> Result<()> {
        let mut builder = FtsIndexBuilder::new("default")?;

        // Add some test documents
        builder.add_intpk_document(1, "hello world")?;
        builder.add_intpk_document(2, "tantivy search engine")?;
        builder.add_intpk_document(3, "full text search")?;

        // Finish and get the files
        let tantivy_files = builder.finish().await?;

        // Verify we have the essential files
        assert!(tantivy_files.contains_key("json0"), "Missing meta.json file");

        // todo: use 

        println!("Generated {} Tantivy files", tantivy_files.len());
        for key in tantivy_files.keys() {
            println!("  - {}", key);
        }

        Ok(())
    }
}