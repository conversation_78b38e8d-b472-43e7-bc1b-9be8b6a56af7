// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::collections::BTreeMap;

use anyhow::{bail, Result};
use tantivy::{
    directory::RamDirectory,
    schema::{Field, NumericOptions, Schema, TextOptions, TextFieldIndexing, IndexRecordOption},
    Index, IndexWriter, SingleSegmentIndexWriter,
};
use tempfile::TempDir;
use tantivy::doc;

use super::dedicated_file::{DedicatedFile, BytesDirectory};

pub const FTS_IDNEX_WRITER_MAX_MEMORY_USAGE: usize = 200 * 1024 * 1024; // 200MB

pub struct FtsIndexBuilder {
    pub index_writer: tantivy::IndexWriter,
    field_body: tantivy::schema::Field,
    pk_field: tantivy::schema::Field,
    temp_dir: TempDir,
}

impl FtsIndexBuilder {
    pub fn new(tokenizer: &str) -> Result<Self> {
        let temp_dir = TempDir::new()?;
        let mut schema_builder = tantivy::schema::Schema::builder();
        let pk_options = NumericOptions::default().set_indexed().set_fast();
        let pk_field = schema_builder.add_u64_field("pk", pk_options);
        let field_body = schema_builder.add_text_field(
            "body",
            tantivy::schema::TextOptions::default().set_indexing_options(
                tantivy::schema::TextFieldIndexing::default()
                    .set_tokenizer(tokenizer)
                    .set_fieldnorms(true)
                    .set_index_option(tantivy::schema::IndexRecordOption::WithFreqsAndPositions),
            ),
        );

        let schema = schema_builder.build();
        let mut index = tantivy::Index::create_in_dir(temp_dir.path(), schema.clone())?;
        index.set_tokenizers(clara_fts::TOKENIZERS.clone());
        let index_writer: tantivy::IndexWriter = index.writer(FTS_IDNEX_WRITER_MAX_MEMORY_USAGE)?;
        Ok(Self {
            index_writer,
            field_body,
            pk_field,
            temp_dir,
        })
    }

    pub fn add_intpk_document(&mut self, pk: u64, body: &str) -> Result<()> {
        self.index_writer.add_document(doc!(self.pk_field => pk, self.field_body => body))?;
        Ok(())
    }

    pub fn finish() -> Result<Bytes> {
        
    }
}