// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::collections::BTreeMap;

use anyhow::Result;
use bytes::Bytes;
use tempfile::TempDir;
use tantivy::doc;
use anyhow::bail;

use super::dedicated_file::BytesDirectory;
use tantivy::schema::NumericOptions;

pub const FTS_IDNEX_WRITER_MAX_MEMORY_USAGE: usize = 200 * 1024 * 1024; // 200MB

pub struct FtsIndexBuilder {
    pub index_writer: tantivy::IndexWriter,
    field_body: tantivy::schema::Field,
    pk_field: tantivy::schema::Field,
    temp_dir: TempDir,
}

impl FtsIndexBuilder {
    pub fn new(tokenizer: &str) -> Result<Self> {
        let temp_dir = TempDir::new()?;
        let mut schema_builder = tantivy::schema::Schema::builder();

        // Add primary key field - numeric fields don't need tokenizers
        // Temporarily comment out pk field to debug the issue
        let pk_options = NumericOptions::default()
            .set_indexed()
            .set_fast();
        let pk_field = schema_builder.add_i64_field("pk_dummy", pk_options);

        // Add body field for full-text search
        let field_body = schema_builder.add_text_field(
            "body",
            tantivy::schema::TextOptions::default()
                .set_indexing_options(
                    tantivy::schema::TextFieldIndexing::default()
                        .set_tokenizer(tokenizer)
                        .set_fieldnorms(true)
                        .set_index_option(tantivy::schema::IndexRecordOption::WithFreqsAndPositions),
                ),
        );

        let schema = schema_builder.build();

        // Create index using IndexBuilder with tokenizers
        let index = tantivy::IndexBuilder::new()
            .tokenizers(clara_fts::TOKENIZERS.clone())
            .schema(schema)
            .create_in_dir(temp_dir.path())?;

        let index_writer: tantivy::IndexWriter = index.writer(FTS_IDNEX_WRITER_MAX_MEMORY_USAGE)?;

        Ok(Self {
            index_writer,
            field_body,
            pk_field,
            temp_dir,
        })
    }

    pub fn add_intpk_document(&mut self, pk: i64, body: &str) -> Result<()> {
        self.index_writer.add_document(doc!(self.pk_field => pk, self.field_body => body))?;
        Ok(())
    }

    pub async fn finish(mut self) -> Result<BTreeMap<String, Vec<u8>>> {
        // Step 1: Commit and finalize the index
        self.index_writer.commit()?;

        let searchable_segment_ids = {
            let index = self.index_writer.index();
            index.searchable_segment_ids()?
        };
        if !searchable_segment_ids.is_empty() {
            self.index_writer.merge(&searchable_segment_ids).await?;
            // A commit is necessary to publish the merge and delete old segments.
            self.index_writer.commit()?;
        }

        {
            let index = self.index_writer.index();
            let len = index.searchable_segment_ids()?.len();
            assert_eq!(len, 1, "Expected 1 segment, got {}", len);
        }

        let index = self.index_writer.index();
        let directory = index.directory();
        let file_lists = directory.list_managed_files();
        let mut tantivy_files = BTreeMap::new();
        for file_path in file_lists {
            let key = file_path
                .file_name()
                .and_then(|n| n.to_str())
                .and_then(|name| match name {
                    "meta.json" => Some("json0"),
                    ".managed.json" => Some("json1"),
                    s if s.ends_with(".term") => Some("term"),
                    s if s.ends_with(".idx") => Some("idx"),
                    s if s.ends_with(".pos") => Some("pos"),
                    s if s.ends_with(".store") => Some("store"),
                    s if s.ends_with(".fast") => Some("fast"),
                    s if s.ends_with(".fieldnorm") => Some("fieldnorm"),
                    _ => None,
                });

            if let Some(key) = key {
                // Read file content immediately while temp_dir is still alive
                let full_path = self.temp_dir.path().join(&file_path);
                let data = std::fs::read(&full_path)?;
                tantivy_files.insert(key.to_string(), data);
            }
        }

        Ok(tantivy_files)
    }

    pub fn get_dir(self) -> TempDir {
        self.temp_dir
    }
}

pub fn create_bytes_directory_from_files(tantivy_files: &BTreeMap<String, Vec<u8>>) -> Result<BytesDirectory> {
    // Convert Vec<u8> to Bytes for efficient access
    let get_bytes = |key: &str| -> Result<Bytes> {
        if let Some(data) = tantivy_files.get(key) {
            Ok(Bytes::from(data.clone()))
        } else {
            if key == "json1" {
                return Ok(Bytes::new());
            }
            bail!("Missing file for key: {}", key);
        }
    };

    Ok(BytesDirectory {
        meta: get_bytes("json0")?,        // meta.json
        managed: get_bytes("json1")?,     // .managed.json
        term: get_bytes("term")?,         // *.term
        idx: get_bytes("idx")?,           // *.idx
        pos: get_bytes("pos")?,           // *.pos
        store: get_bytes("store")?,       // *.store
        fast: get_bytes("fast")?,         // *.fast
        fieldnorm: get_bytes("fieldnorm")?, // *.fieldnorm
    })
}

#[cfg(test)]
mod tests {
    use tantivy::collector::TopDocs;
    use tantivy::schema::Value;

    use super::*;

    #[tokio::test(flavor = "multi_thread")]
    async fn test_fts_index_builder_basic() -> Result<()> {
        let mut builder = FtsIndexBuilder::new("STANDARD_V1")?;
        let pk_field = builder.pk_field;
        let body_field = builder.field_body;
        builder.add_intpk_document(1, "hello world")?;
        builder.add_intpk_document(2, "tantivy search engine")?;
        builder.add_intpk_document(3, "full text search")?;
        let tantivy_files = builder.finish().await?;
        let bytes_dir = create_bytes_directory_from_files(&tantivy_files)?;
        let index = tantivy::Index::open(bytes_dir)?;

        // Verify we can search the index
        let reader = index.reader()?;
        let searcher = reader.searcher();

        let query_parser = tantivy::query::QueryParser::for_index(&index, vec![body_field]);
        let mut found_pks = Vec::new();

        {
            let query = query_parser.parse_query("search")?;
            let top_docs = searcher.search(&query, &TopDocs::with_limit(10))?;
            assert_eq!(top_docs.len(), 2);
            
            for (_, doc_address) in top_docs {
                let retrieved_doc = searcher.doc::<tantivy::TantivyDocument>(doc_address)?;
                let pk_value = retrieved_doc.get_first(pk_field).unwrap().as_i64().unwrap();
                found_pks.push(pk_value);
            }
            assert!(found_pks.contains(&2));
            assert!(found_pks.contains(&3));
        }

        Ok(())
    }
}