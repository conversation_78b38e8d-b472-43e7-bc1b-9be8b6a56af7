// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::collections::BTreeMap;

use anyhow::Result;
use tempfile::TempDir;
use tantivy::doc;
use tantivy::schema::NumericOptions;
use tantivy::{IndexSettings, IndexSortByField, Order};
use tantivy::directory::RamDirectory;

pub const FTS_IDNEX_WRITER_MAX_MEMORY_USAGE: usize = 200 * 1024 * 1024; // 200MB

pub struct FtsIndexBuilder {
    pub index_writer: tantivy::IndexWriter,
    field_body: tantivy::schema::Field,
    pk_field: tantivy::schema::Field,
    temp_dir: TempDir,
}

impl FtsIndexBuilder {
    pub fn new(tokenizer: &str) -> Result<Self> {
        let temp_dir = TempDir::new()?;
        let mut schema_builder = tantivy::schema::Schema::builder();

        // Add primary key field - 必须是fast field用于排序合并
        let pk_options = NumericOptions::default()
            .set_indexed()
            .set_fast()    // 关键：用于排序合并
            .set_stored();
        let pk_field = schema_builder.add_i64_field("pk_dummy", pk_options);

        // Add body field for full-text search
        let field_body = schema_builder.add_text_field(
            "body",
            tantivy::schema::TextOptions::default()
                .set_indexing_options(
                    tantivy::schema::TextFieldIndexing::default()
                        .set_tokenizer(tokenizer)
                        .set_fieldnorms(true)
                        .set_index_option(tantivy::schema::IndexRecordOption::WithFreqsAndPositions),
                ),
        );

        let schema = schema_builder.build();

        // 🔥 设置按PK排序的IndexSettings
        let index_settings = IndexSettings {
            sort_by_field: Some(IndexSortByField {
                field: "pk_dummy".to_string(), // 按pk字段排序
                order: Order::Asc,             // 升序
            }),
            ..Default::default()
        };

        // Create index using IndexBuilder with tokenizers and settings
        let index = tantivy::IndexBuilder::new()
            .tokenizers(clara_fts::TOKENIZERS.clone())
            .schema(schema)
            .settings(index_settings)  // 应用排序设置
            .create_in_dir(temp_dir.path())?;

        let index_writer: tantivy::IndexWriter = index.writer(FTS_IDNEX_WRITER_MAX_MEMORY_USAGE)?;

        Ok(Self {
            index_writer,
            field_body,
            pk_field,
            temp_dir,
        })
    }

    pub fn add_intpk_document(&mut self, pk: i64, body: &str) -> Result<()> {
        self.index_writer.add_document(doc!(self.pk_field => pk, self.field_body => body))?;
        Ok(())
    }

    pub async fn finish(mut self) -> Result<BTreeMap<String, Vec<u8>>> {
        // Step 1: Commit and finalize the index
        self.index_writer.commit()?;

        let searchable_segment_ids = {
            let index = self.index_writer.index();
            index.searchable_segment_ids()?
        };
        if !searchable_segment_ids.is_empty() {
            self.index_writer.merge(&searchable_segment_ids).await?;
            // A commit is necessary to publish the merge and delete old segments.
            self.index_writer.commit()?;
        }

        {
            let index = self.index_writer.index();
            let len = index.searchable_segment_ids()?.len();
            assert_eq!(len, 1, "Expected 1 segment, got {}", len);
        }

        let index = self.index_writer.index();
        let directory = index.directory();
        let file_lists = directory.list_managed_files();
        let mut tantivy_files = BTreeMap::new();
        for file_path in file_lists {
            let key = file_path
                .file_name()
                .and_then(|n| n.to_str())
                .and_then(|name| match name {
                    "meta.json" => Some("json0"),
                    ".managed.json" => Some("json1"),
                    s if s.ends_with(".term") => Some("term"),
                    s if s.ends_with(".idx") => Some("idx"),
                    s if s.ends_with(".pos") => Some("pos"),
                    s if s.ends_with(".store") => Some("store"),
                    s if s.ends_with(".fast") => Some("fast"),
                    s if s.ends_with(".fieldnorm") => Some("fieldnorm"),
                    _ => None,
                });

            if let Some(key) = key {
                // Read file content immediately while temp_dir is still alive
                let full_path = self.temp_dir.path().join(&file_path);
                let data = std::fs::read(&full_path)?;
                tantivy_files.insert(key.to_string(), data);
            }
        }

        Ok(tantivy_files)
    }

    pub fn get_dir(self) -> TempDir {
        self.temp_dir
    }
}

fn merge_sorted_vectors(pks_left: Vec<i64>, pks_right: Vec<i64>) -> Vec<i64> {
    let mut merged = Vec::with_capacity(pks_left.len() + pks_right.len());
    let mut left_idx = 0;
    let mut right_idx = 0;

    while left_idx < pks_left.len() && right_idx < pks_right.len() {
        if pks_left[left_idx] <= pks_right[right_idx] {
            merged.push(pks_left[left_idx]);
            left_idx += 1;
        } else {
            merged.push(pks_right[right_idx]);
            right_idx += 1;
        }
    }

    // Append any remaining elements
    if left_idx < pks_left.len() {
        merged.extend_from_slice(&pks_left[left_idx..]);
    }
    if right_idx < pks_right.len() {
        merged.extend_from_slice(&pks_right[right_idx..]);
    }

    merged
}

pub fn create_bytes_directory_from_files(tantivy_files: BTreeMap<String, Vec<u8>>) -> Result<BytesDirectory> {
    use crate::table::fts::BytesDirectory;
    
    let file_order = [
        "json0", // meta.json
        "json1", // .managed.json
        "term",
        "idx",
        "pos",
        "store",
        "fast",
        "fieldnorm",
    ];
    for name in file_order {
        if let Some(data) = tantivy_files.get(name) {
            let mut range = ftspb::DedicatedFileFileRange::new();
            range.set_offset(self.offset - data_block_start_offset);
            range.set_size(data.len() as u64);

            match name {
                "json0" => tantivy_props.set_meta(range),
                "json1" => tantivy_props.set_managed(range),
                "term" => tantivy_props.set_term(range),
                "idx" => tantivy_props.set_idx(range),
                "pos" => tantivy_props.set_pos(range),
                "store" => tantivy_props.set_store(range),
                "fast" => tantivy_props.set_fast(range),
                "fieldnorm" => tantivy_props.set_fieldnorm(range),
                _ => bail!("match not exist file in tantivy meta files"),
            }

            self.writer.write_all(data)?;
            self.offset += data.len() as u64;

            // Align each file for mmap safety
            let padding = [0u8; 8];
            let pad_len = next_aligned_offset(self.offset as usize, 8) - (self.offset as usize);
            self.writer.write_all(&padding[..pad_len])?;
            self.offset += pad_len as u64;
        }
    }
}

// *************************** generated by claude ***************************
use tantivy::Index;
use tantivy::schema::Schema;
use tantivy::schema::{FAST, INDEXED, TEXT};
type RowData = Vec<(u64, String)>;
use tantivy::indexer::merge_indices;

// 构建带PK排序的索引
fn build_sorted_index(rows: &RowData, index_name: &str) -> tantivy::Result<Index> {
    // 创建schema
    let mut schema_builder = Schema::builder();

    // PK字段 - 用于排序和快速访问
    let pk_field = schema_builder.add_u64_field("pk", FAST | INDEXED);

    // 文本字段 - 用于全文搜索，注意不设置STORED
    let content_field = schema_builder.add_text_field("content", TEXT);

    let schema = schema_builder.build();

    // 关键：设置按PK字段排序
    let settings = IndexSettings {
        sort_by_field: Some(IndexSortByField {
            field: "pk".to_string(),
            order: Order::Asc,
        }),
        ..Default::default()
    };

    // 创建索引
    let index = Index::create(RamDirectory::create(), schema, settings)?;
    let mut writer = index.writer(FTS_IDNEX_WRITER_MAX_MEMORY_USAGE)?;

    println!("构建 {} - 原始插入顺序:", index_name);
    for (i, (pk, content)) in rows.iter().enumerate() {
        println!("  插入位置 {}: PK={}, content='{}'", i, pk, content);
        writer.add_document(doc!(
            pk_field => *pk,
            content_field => content.clone()
        ))?;
    }

    writer.commit()?;

    // 强制只保留一个segment
    let segment_ids = index.searchable_segment_ids()?;
    if segment_ids.len() > 1 {
        writer.merge(&segment_ids).wait()?;
        writer.wait_merging_threads()?;
    }

    println!("  {} 构建完成，segment数量: {}\n", index_name,
            index.searchable_segment_ids()?.len());

    Ok(index)
}

// 合并多个索引
fn merge_sorted_indices(indices: &[Index]) -> tantivy::Result<Index> {
    println!("开始合并 {} 个索引...", indices.len());

    let merged_index = merge_indices(indices, RamDirectory::create())?;

    let segment_count = merged_index.searchable_segment_ids()?.len();
    println!("合并完成，生成segment数量: {}\n", segment_count);

    Ok(merged_index)
}

// 手动合并rows数据（用于验证）
fn merge_rows_by_pk(rows_a: &RowData, rows_b: &RowData) -> RowData {
    let mut result = Vec::new();
    result.extend_from_slice(rows_a);
    result.extend_from_slice(rows_b);

    // 按PK排序
    result.sort_by_key(|(pk, _)| *pk);
    result
}

// *************************** generated by claude ***************************

#[cfg(test)]
mod tests {
    use tantivy::collector::TopDocs;
    use tantivy::schema::Value;

    use super::*;

    // 测试单个索引
    fn test_single_index(index: &Index, rows: &RowData, name: &str) -> tantivy::Result<()> {
        let reader = index.reader()?;
        let searcher = reader.searcher();
        let schema = index.schema();

        let pk_field = schema.get_field("pk").unwrap();

        println!("测试 {} - doc_id 到 PK 的映射:", name);

        // 验证doc_id和PK的对应关系
        let segment_reader = searcher.segment_reader(0);
        let pk_accessor = segment_reader.fast_fields().u64("pk").unwrap();

        for doc_id in 0..segment_reader.num_docs() {
            let pk_value = pk_accessor.values.get_val(doc_id);
            println!("  doc_id {} -> PK {}", doc_id, pk_value);

            // 在原始rows中找到对应的PK
            let expected_index = rows.iter()
                .enumerate()
                .find(|(_, (pk, _))| *pk == pk_value)
                .map(|(i, _)| i);

            if let Some(expected_index) = expected_index {
                println!("    对应原始数据 rows[{}] = ({}, '{}')",
                        expected_index, rows[expected_index].0, rows[expected_index].1);
            }
        }
        println!();

        Ok(())
    }

    // 测试合并后的索引
    fn test_merged_index(index: &Index, rows_c: &RowData) -> tantivy::Result<()> {
        let reader = index.reader()?;
        let searcher = reader.searcher();

        println!("测试合并后索引 - 验证 doc_id 和 rows_c 的对应关系:");

        let segment_reader = searcher.segment_reader(0);
        let pk_accessor = segment_reader.fast_fields().u64("pk").unwrap();

        for doc_id in 0..segment_reader.num_docs() {
            let pk_from_index = pk_accessor.values.get_val(doc_id);
            let (pk_from_rows, content) = &rows_c[doc_id as usize];

            println!("  doc_id {} -> 索引中PK: {}, rows_c[{}]: ({}, '{}')",
                    doc_id, pk_from_index, doc_id, pk_from_rows, content);

            // 验证对应关系是否正确
            if pk_from_index == *pk_from_rows {
                println!("    ✅ 映射正确");
            } else {
                println!("    ❌ 映射错误!");
            }
        }

        // 测试搜索功能
        println!("\n测试搜索功能:");
        test_search(index, "fruit")?;
        test_search(index, "yellow")?;

        Ok(())
    }

    // 测试搜索功能
    fn test_search(index: &Index, query_str: &str) -> tantivy::Result<()> {
        use tantivy::collector::TopDocs;
        use tantivy::query::QueryParser;

        let reader = index.reader()?;
        let searcher = reader.searcher();
        let schema = index.schema();

        let content_field = schema.get_field("content").unwrap();
        let pk_field = schema.get_field("pk").unwrap();

        let query_parser = QueryParser::for_index(index, vec![content_field]);
        let query = query_parser.parse_query(query_str)?;

        let top_docs = searcher.search(&query, &TopDocs::with_limit(10))?;

        println!("搜索 '{}' 结果:", query_str);
        for (score, doc_address) in top_docs {
            let doc_id = doc_address.doc_id;

            // 获取PK值
            let segment_reader = searcher.segment_reader(doc_address.segment_ord);
            let pk_accessor = segment_reader.fast_fields().u64("pk").unwrap();
            let pk_value = pk_accessor.values.get_val(doc_id);

            println!("  doc_id: {}, PK: {}, score: {:.4}", doc_id, pk_value, score);
        }
        println!();

        Ok(())
    }

    #[test]
    fn test_merge_index() -> Result<()>{
        let rows_a: RowData = vec![
            (10, "apple fruit red delicious".to_string()),
            (30, "banana fruit yellow sweet".to_string()),
            (50, "cherry fruit red small".to_string()),
        ];

        let rows_b: RowData = vec![
            (20, "orange fruit orange citrus".to_string()),
            (40, "grape fruit purple cluster".to_string()),
            (60, "mango fruit yellow tropical".to_string()),
        ];

        // 2. 构建两个排序索引
        let index_a = build_sorted_index(&rows_a, "Index A")?;
        let index_b = build_sorted_index(&rows_b, "Index B")?;

        // 3. 验证单个索引的搜索
        println!("=== 验证单个索引 ===");
        test_single_index(&index_a, &rows_a, "Index A")?;
        test_single_index(&index_b, &rows_b, "Index B")?;

        println!("=== 合并索引 ===");
        let index_c = merge_sorted_indices(&[index_a, index_b])?;

        // 5. 构建合并后的 rows_c（手动合并，用于验证）
        let rows_c = merge_rows_by_pk(&rows_a, &rows_b);
        println!("合并后的 rows_c: {:?}", rows_c);

        // 6. 验证合并结果
        println!("=== 验证合并结果 ===");
        test_merged_index(&index_c, &rows_c)?;

        Ok(())
    }

    #[tokio::test(flavor = "multi_thread")]
    async fn test_fts_index_builder_basic() -> Result<()> {
        let mut builder = FtsIndexBuilder::new("STANDARD_V1")?;
        let pk_field = builder.pk_field;
        let body_field = builder.field_body;
        builder.add_intpk_document(1, "hello world")?;
        builder.add_intpk_document(2, "tantivy search engine")?;
        builder.add_intpk_document(3, "full text search")?;
        let tantivy_files = builder.finish().await?;
        let bytes_dir = create_bytes_directory_from_files(&tantivy_files)?;
        let mut index = tantivy::Index::open(bytes_dir)?;
        index.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // Verify we can search the index
        let reader = index.reader()?;
        let searcher = reader.searcher();

        let query_parser = tantivy::query::QueryParser::for_index(&index, vec![body_field]);
        let mut found_pks = Vec::new();

        {
            let query = query_parser.parse_query("search")?;
            let top_docs = searcher.search(&query, &TopDocs::with_limit(10))?;
            assert_eq!(top_docs.len(), 2);

            for (_, doc_address) in top_docs {
                let retrieved_doc = searcher.doc::<tantivy::TantivyDocument>(doc_address)?;
                let pk_value = retrieved_doc.get_first(pk_field).unwrap().as_i64().unwrap();
                found_pks.push(pk_value);
            }
            assert!(found_pks.contains(&2));
            assert!(found_pks.contains(&3));
        }

        Ok(())
    }

    #[test]
    fn test_merge_sorted_vectors() {
        let pks_left = vec![1, 3, 5, 7];
        let pks_right = vec![2, 4, 6, 8, 10];

        let merged = merge_sorted_vectors(pks_left, pks_right);
        assert_eq!(merged, vec![1, 2, 3, 4, 5, 6, 7, 8, 10]);
    }
}