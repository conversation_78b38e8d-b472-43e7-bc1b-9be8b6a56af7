// Copyright 2025-present PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package enginepb;

message FullTextIndexDef {
  int64 index_id = 1;
  int64 col_id = 2;

  string parser_type = 10;
}

// ----------------- PackedFile -----------------
message PackedFileIndexBlock {
  repeated bytes data_block_start_keys = 1;

  // The absolute offset of the data block in the packed file.
  // It contains N+1 data blocks. The last offset is the end of the last data block.
  // Note that data block may contain padding bytes at the beginning.
  // For this reason, do not read data block from the beginning of the offset.
  repeated uint32 data_block_offsets = 2;
}

message PackedFilePropertyBlock {
  // Smallest LogicalPartitionKey in the file.
  // Used to quickly filter out LPs
  bytes smallest_lp_key = 1;
  // Largest LogicalPartitionKey in the file.
  // Used to quickly filter out LPs
  bytes largest_lp_key = 2;
  // Total number of logical partitions
  uint32 total_lps = 3;
  // Total number of keys (docs)
  uint32 total_keys = 4;
  // Total number of bytes of Tantivy indexes
  uint32 total_index_bytes = 5;
  // Total number of bytes of all Primary Keys
  uint32 total_keys_bytes = 6;
}
// ----------------- PackedFile -----------------

// ----------------- DedicatedFile -----------------

// Defines a byte range [offset, offset+size) in a file.
message DedicatedFileFileRange {
  uint64 offset = 1;
  uint64 size = 2;
}

// Describes the locations of various Tantivy index file components within the Data Block.
message DedicatedFileTantivyFiles {
  DedicatedFileFileRange meta = 1;      // meta.json
  DedicatedFileFileRange managed = 2;      // .managed.json
  DedicatedFileFileRange term = 3;      // .term
  DedicatedFileFileRange idx = 4;       // .idx
  DedicatedFileFileRange pos = 5;       // .pos
  DedicatedFileFileRange store = 6;     // .store
  DedicatedFileFileRange fast = 7;      // .fast
  DedicatedFileFileRange fieldnorm = 8; // .fieldnorm
}

// Handle Index Block
// Used to quickly locate the Handle Block that a handle (PK) resides in.
message DedicatedFileHandleIndexBlock {
  // The start primary key of each Handle Block.
  // Note: the number of start_keys here is one less than the number of offsets.
  repeated bytes handle_block_start_key = 1;

  // The absolute offset of each Handle Block in the file.
  // Contains N+1 elements, where the last element is the end offset of the last block.
  repeated uint64 handle_block_offsets = 2;
}

// Property Block
message DedicatedFilePropertyBlock {
  // The LogicalPartitionKey that this file serves.
  bytes lp_key = 1;
  // Whether the primary key is of integer type (int handle).
  bool is_int_handle = 2;
  // Location information of all Tantivy index files within the Data Block.
  DedicatedFileTantivyFiles tantivy_files = 10;
}
// ----------------- DedicatedFile -----------------

